{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "strict": true, "alwaysStrict": true, "strictNullChecks": true, "noUnusedLocals": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitThis": true, "noImplicitReturns": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "downlevelIteration": true, "allowSyntheticDefaultImports": true, "types": ["react-spring", "jest"]}, "exclude": ["node_modules", "cypress"], "include": ["./src/**/*.ts", "./src/**/*.tsx"]}