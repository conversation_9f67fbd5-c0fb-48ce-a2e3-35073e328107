# See https://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
/node_modules

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

/.netlify

npm-debug.log*
yarn-debug.log*
yarn-error.log*

notes.txt
.idea/

.vscode/
Intro.md
package-lock.json

cypress/videos
cypress/screenshots
cypress/fixtures/example.json