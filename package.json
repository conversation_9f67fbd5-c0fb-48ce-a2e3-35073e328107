{"name": "SepoSwap", "description": "The Gateway to Base Sepolia DeFi", "homepage": ".", "private": true, "devDependencies": {"@ethersproject/experimental": "^5.0.1", "@popperjs/core": "^2.4.4", "@reach/dialog": "^0.10.3", "@reach/portal": "^0.10.3", "@reduxjs/toolkit": "^1.3.5", "@types/jest": "^25.2.1", "@types/lodash.flatmap": "^4.5.6", "@types/multicodec": "^1.0.0", "@types/node": "^13.13.5", "@types/qs": "^6.9.2", "@types/react": "^16.9.34", "@types/react-dom": "^16.9.7", "@types/react-redux": "^7.1.8", "@types/react-router-dom": "^5.0.0", "@types/react-virtualized-auto-sizer": "^1.0.0", "@types/react-window": "^1.8.2", "@types/rebass": "^4.0.5", "@types/styled-components": "^5.1.0", "@types/testing-library__cypress": "^5.0.5", "@typescript-eslint/eslint-plugin": "^2.31.0", "@typescript-eslint/parser": "^2.31.0", "@uniswap/default-token-list": "^1.3.1", "@uniswap/token-lists": "^1.0.0-beta.15", "@uniswap/v2-core": "1.0.0", "@uniswap/v2-periphery": "^1.1.0-beta.0", "@web3-react/core": "^6.0.9", "@web3-react/fortmatic-connector": "^6.0.9", "@web3-react/injected-connector": "^6.0.7", "@web3-react/portis-connector": "^6.0.9", "@web3-react/walletconnect-connector": "^6.1.1", "@web3-react/walletlink-connector": "^6.0.9", "ajv": "^6.12.3", "cids": "^1.0.0", "copy-to-clipboard": "^3.2.0", "cross-env": "^7.0.2", "cypress": "^4.11.0", "eslint": "^6.8.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.0.0", "ethers": "^5.0.7", "i18next": "^15.0.9", "i18next-browser-languagedetector": "^3.0.1", "i18next-xhr-backend": "^2.0.1", "inter-ui": "^3.13.1", "jazzicon": "^1.5.0", "lodash.flatmap": "^4.5.0", "multicodec": "^2.0.0", "multihashes": "^3.0.1", "polished": "^3.3.2", "prettier": "^1.17.0", "qs": "^6.9.4", "react": "^16.13.1", "react-device-detect": "^1.6.2", "react-dom": "^16.13.1", "react-feather": "^2.0.8", "react-ga": "^2.5.7", "react-i18next": "^10.7.0", "react-popper": "^2.2.3", "react-redux": "^7.2.0", "react-router-dom": "^5.0.0", "react-scripts": "^3.4.1", "react-spring": "^8.0.27", "react-use-gesture": "^6.0.14", "react-virtualized-auto-sizer": "^1.0.2", "react-window": "^1.8.5", "rebass": "^4.0.7", "redux-localstorage-simple": "^2.3.1", "serve": "^11.3.0", "start-server-and-test": "^1.11.0", "styled-components": "^4.2.0", "typescript": "^3.8.3"}, "resolutions": {"@walletconnect/web3-provider": "1.1.1-alpha.0"}, "scripts": {"deploy": "gh-pages -d build", "start": "NODE_OPTIONS='--openssl-legacy-provider' react-scripts start", "build": "NODE_OPTIONS='--openssl-legacy-provider' react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "integration-test": "start-server-and-test 'serve build -l 3000' http://localhost:3000 'cypress run'"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "license": "GPL-3.0-or-later", "dependencies": {"gh-pages": "^6.3.0", "nodeswapv2-sdk": "^3.0.2"}}